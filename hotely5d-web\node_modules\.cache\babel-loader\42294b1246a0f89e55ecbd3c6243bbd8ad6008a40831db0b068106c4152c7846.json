{"ast": null, "code": "import \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { exportMemberAPI, importMemberAPI, downloadMemberTemplateAPI } from '@/api/member';\nexport default {\n  name: 'ExcelDemo',\n  data() {\n    return {\n      loading: false\n    };\n  },\n  methods: {\n    // 导出会员数据\n    async exportMemberData() {\n      try {\n        this.loading = true;\n        const response = await exportMemberAPI({});\n\n        // 创建下载链接\n        const blob = new Blob([response], {\n          type: 'application/vnd.ms-excel'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `会员数据_${new Date().getTime()}.xls`;\n        link.style.display = 'none';\n\n        // 添加到DOM中，触发下载，然后移除\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n        this.$message.success('导出成功');\n      } catch (error) {\n        console.error('导出错误:', error);\n        this.$message.error('导出失败：' + (error.response?.data?.message || error.message));\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 下载会员导入模板\n    async downloadMemberTemplate() {\n      try {\n        this.loading = true;\n        const response = await downloadMemberTemplateAPI();\n\n        // 创建下载链接\n        const blob = new Blob([response], {\n          type: 'application/vnd.ms-excel'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = '会员导入模板.xls';\n        link.click();\n        window.URL.revokeObjectURL(url);\n        this.$message.success('模板下载成功');\n      } catch (error) {\n        this.$message.error('下载失败：' + error.message);\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 导入会员数据\n    async handleMemberImport(file) {\n      try {\n        this.loading = true;\n        const formData = new FormData();\n        formData.append('file', file);\n        const response = await importMemberAPI(formData);\n        if (response.flag) {\n          this.$message.success(response.message);\n        } else {\n          this.$message.error(response.message);\n        }\n      } catch (error) {\n        this.$message.error('导入失败：' + error.message);\n      } finally {\n        this.loading = false;\n      }\n      return false; // 阻止自动上传\n    },\n\n    // 导出订单数据\n    async exportOrderData() {\n      try {\n        this.loading = true;\n        // 这里需要调用订单导出API\n        this.$message.info('订单导出功能开发中...');\n      } catch (error) {\n        this.$message.error('导出失败：' + error.message);\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["exportMemberAPI", "importMemberAPI", "downloadMemberTemplateAPI", "name", "data", "loading", "methods", "exportMemberData", "response", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "Date", "getTime", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "$message", "success", "error", "console", "message", "downloadMemberTemplate", "handleMemberImport", "file", "formData", "FormData", "append", "flag", "exportOrderData", "info"], "sources": ["src/views/ExcelDemo.vue"], "sourcesContent": ["<template>\n  <div class=\"excel-demo\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>EasyPoi Excel导入导出演示</span>\n      </div>\n      \n      <!-- 会员数据导入导出 -->\n      <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n        <el-col :span=\"24\">\n          <h3>会员数据管理</h3>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n        <el-col :span=\"6\">\n          <el-button type=\"primary\" @click=\"exportMemberData\">\n            <i class=\"el-icon-download\"></i> 导出会员数据\n          </el-button>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-button type=\"success\" @click=\"downloadMemberTemplate\">\n            <i class=\"el-icon-document\"></i> 下载导入模板\n          </el-button>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-upload\n            class=\"upload-demo\"\n            action=\"\"\n            :before-upload=\"handleMemberImport\"\n            :show-file-list=\"false\"\n            accept=\".xls,.xlsx\">\n            <el-button type=\"warning\">\n              <i class=\"el-icon-upload2\"></i> 导入会员数据\n            </el-button>\n          </el-upload>\n        </el-col>\n      </el-row>\n\n      <el-divider></el-divider>\n\n      <!-- 订单数据导出 -->\n      <el-row :gutter=\"20\" style=\"margin-bottom: 20px;\">\n        <el-col :span=\"24\">\n          <h3>订单数据管理</h3>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <el-button type=\"primary\" @click=\"exportOrderData\">\n            <i class=\"el-icon-download\"></i> 导出订单数据\n          </el-button>\n        </el-col>\n      </el-row>\n\n      <!-- 功能说明 -->\n      <el-divider></el-divider>\n      <el-row>\n        <el-col :span=\"24\">\n          <h3>EasyPoi功能特点</h3>\n          <ul>\n            <li><strong>注解驱动：</strong>通过@Excel注解配置列映射，简单易用</li>\n            <li><strong>数据转换：</strong>支持数字、日期格式化，枚举值替换</li>\n            <li><strong>模板导出：</strong>可以创建标准的Excel模板供用户下载</li>\n            <li><strong>批量导入：</strong>支持Excel文件批量导入数据到数据库</li>\n            <li><strong>错误处理：</strong>导入时自动处理数据验证和错误提示</li>\n            <li><strong>性能优化：</strong>针对大数据量进行了优化处理</li>\n          </ul>\n        </el-col>\n      </el-row>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { \n  exportMemberAPI, \n  importMemberAPI, \n  downloadMemberTemplateAPI \n} from '@/api/member'\n\nexport default {\n  name: 'ExcelDemo',\n  data() {\n    return {\n      loading: false\n    }\n  },\n  methods: {\n    // 导出会员数据\n    async exportMemberData() {\n      try {\n        this.loading = true\n        const response = await exportMemberAPI({})\n\n        // 创建下载链接\n        const blob = new Blob([response], {\n          type: 'application/vnd.ms-excel'\n        })\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = `会员数据_${new Date().getTime()}.xls`\n        link.style.display = 'none'\n\n        // 添加到DOM中，触发下载，然后移除\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        this.$message.success('导出成功')\n      } catch (error) {\n        console.error('导出错误:', error)\n        this.$message.error('导出失败：' + (error.response?.data?.message || error.message))\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 下载会员导入模板\n    async downloadMemberTemplate() {\n      try {\n        this.loading = true\n        const response = await downloadMemberTemplateAPI()\n        \n        // 创建下载链接\n        const blob = new Blob([response], {\n          type: 'application/vnd.ms-excel'\n        })\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = '会员导入模板.xls'\n        link.click()\n        window.URL.revokeObjectURL(url)\n        \n        this.$message.success('模板下载成功')\n      } catch (error) {\n        this.$message.error('下载失败：' + error.message)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 导入会员数据\n    async handleMemberImport(file) {\n      try {\n        this.loading = true\n        const formData = new FormData()\n        formData.append('file', file)\n        \n        const response = await importMemberAPI(formData)\n        \n        if (response.flag) {\n          this.$message.success(response.message)\n        } else {\n          this.$message.error(response.message)\n        }\n      } catch (error) {\n        this.$message.error('导入失败：' + error.message)\n      } finally {\n        this.loading = false\n      }\n      \n      return false // 阻止自动上传\n    },\n\n    // 导出订单数据\n    async exportOrderData() {\n      try {\n        this.loading = true\n        // 这里需要调用订单导出API\n        this.$message.info('订单导出功能开发中...')\n      } catch (error) {\n        this.$message.error('导出失败：' + error.message)\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.excel-demo {\n  padding: 20px;\n}\n\n.box-card {\n  margin-bottom: 20px;\n}\n\n.clearfix:before,\n.clearfix:after {\n  display: table;\n  content: \"\";\n}\n\n.clearfix:after {\n  clear: both;\n}\n\n.upload-demo {\n  display: inline-block;\n}\n\nul {\n  padding-left: 20px;\n}\n\nli {\n  margin-bottom: 8px;\n  line-height: 1.6;\n}\n</style>\n"], "mappings": ";;;AA4EA,SACAA,eAAA,EACAC,eAAA,EACAC,yBAAA,QACA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACA,MAAAC,iBAAA;MACA;QACA,KAAAF,OAAA;QACA,MAAAG,QAAA,SAAAR,eAAA;;QAEA;QACA,MAAAS,IAAA,OAAAC,IAAA,EAAAF,QAAA;UACAG,IAAA;QACA;QACA,MAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;QACA,MAAAO,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,IAAA,CAAAI,QAAA,eAAAC,IAAA,GAAAC,OAAA;QACAN,IAAA,CAAAO,KAAA,CAAAC,OAAA;;QAEA;QACAP,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,IAAA;QACAA,IAAA,CAAAW,KAAA;QACAV,QAAA,CAAAQ,IAAA,CAAAG,WAAA,CAAAZ,IAAA;QACAH,MAAA,CAAAC,GAAA,CAAAe,eAAA,CAAAjB,GAAA;QAEA,KAAAkB,QAAA,CAAAC,OAAA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA,YAAAA,KAAA,CAAAxB,QAAA,EAAAJ,IAAA,EAAA8B,OAAA,IAAAF,KAAA,CAAAE,OAAA;MACA;QACA,KAAA7B,OAAA;MACA;IACA;IAEA;IACA,MAAA8B,uBAAA;MACA;QACA,KAAA9B,OAAA;QACA,MAAAG,QAAA,SAAAN,yBAAA;;QAEA;QACA,MAAAO,IAAA,OAAAC,IAAA,EAAAF,QAAA;UACAG,IAAA;QACA;QACA,MAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;QACA,MAAAO,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAP,GAAA;QACAI,IAAA,CAAAI,QAAA;QACAJ,IAAA,CAAAW,KAAA;QACAd,MAAA,CAAAC,GAAA,CAAAe,eAAA,CAAAjB,GAAA;QAEA,KAAAkB,QAAA,CAAAC,OAAA;MACA,SAAAC,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA,WAAAA,KAAA,CAAAE,OAAA;MACA;QACA,KAAA7B,OAAA;MACA;IACA;IAEA;IACA,MAAA+B,mBAAAC,IAAA;MACA;QACA,KAAAhC,OAAA;QACA,MAAAiC,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,SAAAH,IAAA;QAEA,MAAA7B,QAAA,SAAAP,eAAA,CAAAqC,QAAA;QAEA,IAAA9B,QAAA,CAAAiC,IAAA;UACA,KAAAX,QAAA,CAAAC,OAAA,CAAAvB,QAAA,CAAA0B,OAAA;QACA;UACA,KAAAJ,QAAA,CAAAE,KAAA,CAAAxB,QAAA,CAAA0B,OAAA;QACA;MACA,SAAAF,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA,WAAAA,KAAA,CAAAE,OAAA;MACA;QACA,KAAA7B,OAAA;MACA;MAEA;IACA;;IAEA;IACA,MAAAqC,gBAAA;MACA;QACA,KAAArC,OAAA;QACA;QACA,KAAAyB,QAAA,CAAAa,IAAA;MACA,SAAAX,KAAA;QACA,KAAAF,QAAA,CAAAE,KAAA,WAAAA,KAAA,CAAAE,OAAA;MACA;QACA,KAAA7B,OAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}